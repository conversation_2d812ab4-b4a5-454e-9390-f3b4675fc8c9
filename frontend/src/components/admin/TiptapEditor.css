/* Стили для Tiptap редактора */

.ProseMirror {
  outline: none;
  min-height: 250px;
  padding: 1rem;
  line-height: 1.6;
  font-size: 14px;
  color: #1f2937 !important; /* Принудительно устанавливаем темный цвет текста */
  background-color: #ffffff !important; /* Убеждаемся, что фон белый */
}

/* Общее правило для всех текстовых элементов в редакторе */
.ProseMirror * {
  color: inherit !important;
}

.ProseMirror:focus {
  outline: none;
}

/* Заголовки */
.ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #1f2937 !important;
  line-height: 1.2;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: #374151 !important;
  line-height: 1.3;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #4b5563 !important;
  line-height: 1.4;
}

.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0.75rem 0 0.5rem 0;
  color: #6b7280 !important;
  line-height: 1.4;
}

/* Абзацы */
.ProseMirror p {
  margin: 0.75rem 0;
  line-height: 1.6;
  color: #1f2937 !important; /* Принудительно устанавливаем темный цвет для абзацев */
}

.ProseMirror p:first-child {
  margin-top: 0;
}

.ProseMirror p:last-child {
  margin-bottom: 0;
}

/* Списки */
.ProseMirror ul,
.ProseMirror ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.ProseMirror ul li,
.ProseMirror ol li {
  margin: 0.25rem 0;
  line-height: 1.6;
  color: #1f2937 !important; /* Принудительно устанавливаем темный цвет для элементов списка */
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

/* Список задач */
.ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;
}

.ProseMirror ul[data-type="taskList"] li > label {
  margin-right: 0.5rem;
  margin-top: 0.1rem;
  user-select: none;
}

.ProseMirror ul[data-type="taskList"] li > div {
  flex: 1;
}

/* Цитаты */
.ProseMirror blockquote {
  border-left: 4px solid #8BC34A;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

/* Блоки кода */
.ProseMirror pre {
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

/* Ссылки */
.ProseMirror a {
  color: #8BC34A;
  text-decoration: underline;
  cursor: pointer;
}

.ProseMirror a:hover {
  color: #4E8C29;
}

/* Изображения */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Таблицы */
.ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  margin: 1.5rem 0;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  overflow: hidden;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #d1d5db;
  padding: 0.75rem;
  text-align: left;
  vertical-align: top;
}

.ProseMirror table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.ProseMirror table tr:nth-child(even) {
  background-color: #f9fafb;
}

/* Горизонтальная линия */
.ProseMirror hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2rem 0;
}

/* Выделение */
.ProseMirror mark {
  background-color: #fef3c7;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Верхний и нижний индекс */
.ProseMirror sup {
  font-size: 0.75rem;
  vertical-align: super;
}

.ProseMirror sub {
  font-size: 0.75rem;
  vertical-align: sub;
}

/* Placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Выделение текста */
.ProseMirror ::selection {
  background-color: #8BC34A;
  color: white;
}

/* Фокус на элементах */
.ProseMirror *:focus {
  outline: 2px solid #8BC34A;
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* Анимации */
.ProseMirror * {
  transition: all 0.2s ease-in-out;
}

/* Адаптивность */
@media (max-width: 768px) {
  .ProseMirror {
    padding: 0.75rem;
    font-size: 16px; /* Предотвращает зум на iOS */
  }
  
  .ProseMirror h1 {
    font-size: 1.75rem;
  }
  
  .ProseMirror h2 {
    font-size: 1.375rem;
  }
  
  .ProseMirror h3 {
    font-size: 1.125rem;
  }
  
  .ProseMirror table {
    font-size: 0.875rem;
  }
  
  .ProseMirror table td,
  .ProseMirror table th {
    padding: 0.5rem;
  }
}

/* Принудительная светлая тема для редактора */
.tiptap-light-theme .ProseMirror,
.ProseMirror {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

.tiptap-light-theme .ProseMirror p,
.ProseMirror p {
  color: #1f2937 !important;
}

.tiptap-light-theme .ProseMirror h1,
.tiptap-light-theme .ProseMirror h2,
.tiptap-light-theme .ProseMirror h3,
.tiptap-light-theme .ProseMirror h4,
.tiptap-light-theme .ProseMirror h5,
.tiptap-light-theme .ProseMirror h6 {
  color: #1f2937 !important;
}

.tiptap-light-theme .ProseMirror ul li,
.tiptap-light-theme .ProseMirror ol li {
  color: #1f2937 !important;
}

/* Темная тема (только при явном указании класса) */
.tiptap-dark-theme .ProseMirror {
  color: #f9fafb !important;
  background-color: #1f2937 !important;
}

.tiptap-dark-theme .ProseMirror h1,
.tiptap-dark-theme .ProseMirror h2,
.tiptap-dark-theme .ProseMirror h3,
.tiptap-dark-theme .ProseMirror h4,
.tiptap-dark-theme .ProseMirror h5,
.tiptap-dark-theme .ProseMirror h6 {
  color: #f3f4f6 !important;
}

.tiptap-dark-theme .ProseMirror p,
.tiptap-dark-theme .ProseMirror ul li,
.tiptap-dark-theme .ProseMirror ol li {
  color: #f9fafb !important;
}

.tiptap-dark-theme .ProseMirror blockquote {
  background-color: #374151;
  color: #d1d5db;
}

.tiptap-dark-theme .ProseMirror pre,
.tiptap-dark-theme .ProseMirror code {
  background-color: #374151;
  color: #f3f4f6;
}

.tiptap-dark-theme .ProseMirror table th {
  background-color: #374151;
  color: #f3f4f6;
}

.tiptap-dark-theme .ProseMirror table tr:nth-child(even) {
  background-color: #374151;
}
